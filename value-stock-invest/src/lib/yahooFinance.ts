import { StockData, SearchResult } from '@/types/stock';
import { DatabaseService, supabase } from './supabase';
import { StockScraper } from './stockScraper';

// Types for Yahoo Finance API responses (kept for compatibility)
export interface StockQuote {
  symbol: string;
  regularMarketPrice: number;
  regularMarketChange: number;
  regularMarketChangePercent: number;
  regularMarketVolume: number;
  marketCap: number;
  trailingPE?: number;
  forwardPE?: number;
  priceToBook?: number;
  dividendYield?: number;
  beta?: number;
  fiftyTwoWeekLow: number;
  fiftyTwoWeekHigh: number;
  shortName: string;
  longName: string;
  currency: string;
  exchange: string;
}

export interface FinancialData {
  totalRevenue: number;
  totalDebt: number;
  totalCash: number;
  freeCashflow: number;
  operatingCashflow: number;
  revenueGrowth: number;
  earningsGrowth: number;
  grossMargins: number;
  operatingMargins: number;
  profitMargins: number;
  returnOnAssets: number;
  returnOnEquity: number;
  debtToEquity: number;
  currentRatio: number;
  quickRatio: number;
}

export interface StockDataLocal {
  quote: StockQuote;
  financials: FinancialData;
  keyStats: {
    pegRatio?: number;
    priceToSales: number;
    enterpriseValue: number;
    enterpriseToRevenue: number;
    enterpriseToEbitda: number;
    bookValue: number;
    sharesOutstanding: number;
    floatShares: number;
    heldPercentInsiders: number;
    heldPercentInstitutions: number;
  };
}

// Local data service using Supabase database with web scraping
export class YahooFinanceService {
  // Check if data is fresh (less than 10 minutes old)
  private static isDataFresh(timestamp: string, maxAgeMinutes: number = 10): boolean {
    const now = new Date();
    const dataTime = new Date(timestamp);
    const ageMinutes = (now.getTime() - dataTime.getTime()) / (1000 * 60);
    return ageMinutes < maxAgeMinutes;
  }

  // Trigger scraping if data is stale (disabled for now due to scraping issues)
  private static async ensureFreshData(symbol: string): Promise<void> {
    // Temporarily disabled automatic scraping due to Yahoo Finance structure changes
    // const freshness = await DatabaseService.getDataFreshness(symbol);
    //
    // // If quote data is older than 10 minutes, trigger a scrape
    // if (!freshness.quote_age || freshness.quote_age > 10) {
    //   console.log(`Data for ${symbol} is stale, triggering scrape...`);
    //   try {
    //     await StockScraper.scrapeCompleteStockData(symbol);
    //   } catch (error) {
    //     console.warn(`Failed to scrape fresh data for ${symbol}:`, error);
    //   }
    // }

    console.log(`Skipping scraping for ${symbol} - using database data only`);
  }
  // Get stock quote data from local database
  static async getStockQuote(symbol: string): Promise<StockQuote | null> {
    try {
      // Ensure we have fresh data
      await this.ensureFreshData(symbol);

      // Get latest quote from database
      const quote = await DatabaseService.getLatestQuote(symbol);
      const stock = await DatabaseService.getStock(symbol);

      if (!quote || !stock) {
        return null;
      }

      return {
        symbol: quote.symbol,
        regularMarketPrice: quote.price,
        regularMarketChange: quote.change_amount || 0,
        regularMarketChangePercent: quote.change_percent || 0,
        regularMarketVolume: quote.volume || 0,
        marketCap: quote.market_cap || 0,
        trailingPE: quote.pe_ratio,
        forwardPE: quote.forward_pe,
        priceToBook: quote.price_to_book,
        dividendYield: quote.dividend_yield,
        beta: quote.beta,
        fiftyTwoWeekLow: quote.week_52_low || 0,
        fiftyTwoWeekHigh: quote.week_52_high || 0,
        shortName: stock.name,
        longName: stock.name,
        currency: quote.currency,
        exchange: stock.exchange || 'UNKNOWN'
      };
    } catch (error) {
      console.error(`Error fetching quote for ${symbol}:`, error);
      return null;
    }
  }

  // Get financial data from local database
  static async getFinancialData(symbol: string): Promise<FinancialData | null> {
    try {
      // Ensure we have fresh data
      await this.ensureFreshData(symbol);

      // Get latest financials from database
      const financials = await DatabaseService.getLatestFinancials(symbol);

      if (!financials) {
        return null;
      }

      return {
        totalRevenue: financials.total_revenue || 0,
        totalDebt: financials.total_debt || 0,
        totalCash: financials.total_cash || 0,
        freeCashflow: financials.free_cashflow || 0,
        operatingCashflow: financials.operating_cashflow || 0,
        revenueGrowth: financials.revenue_growth || 0,
        earningsGrowth: financials.earnings_growth || 0,
        grossMargins: financials.gross_margins || 0,
        operatingMargins: financials.operating_margins || 0,
        profitMargins: financials.profit_margins || 0,
        returnOnAssets: financials.return_on_assets || 0,
        returnOnEquity: financials.return_on_equity || 0,
        debtToEquity: financials.debt_to_equity || 0,
        currentRatio: financials.current_ratio || 0,
        quickRatio: financials.quick_ratio || 0
      };
    } catch (error) {
      console.error(`Error fetching financials for ${symbol}:`, error);
      return null;
    }
  }

  // Get complete stock data from local database
  static async getStockData(symbol: string): Promise<StockDataLocal | null> {
    try {
      // Ensure we have fresh data
      await this.ensureFreshData(symbol);

      const [quote, financials] = await Promise.all([
        this.getStockQuote(symbol),
        this.getFinancialData(symbol)
      ]);

      if (!quote || !financials) {
        console.warn(`Missing database data for ${symbol}. Quote: ${!!quote}, Financials: ${!!financials}`);
        return null; // Return null instead of throwing error
      }

      // Get additional data from financials table
      const dbFinancials = await DatabaseService.getLatestFinancials(symbol);

      return {
        quote,
        financials,
        keyStats: {
          pegRatio: dbFinancials?.peg_ratio,
          priceToSales: 0, // Would need to calculate or scrape
          enterpriseValue: dbFinancials?.enterprise_value || 0,
          enterpriseToRevenue: 0, // Would need to calculate
          enterpriseToEbitda: 0, // Would need to calculate
          bookValue: dbFinancials?.book_value || 0,
          sharesOutstanding: dbFinancials?.shares_outstanding || 0,
          floatShares: 0, // Would need to scrape
          heldPercentInsiders: 0, // Would need to scrape
          heldPercentInstitutions: 0 // Would need to scrape
        }
      };
    } catch (error) {
      console.error(`Error fetching complete stock data for ${symbol}:`, error);
      return null; // Return null instead of throwing error
    }
  }

  // Search for stocks in local database
  static async searchStocks(query: string): Promise<Array<{symbol: string, name: string}>> {
    try {
      // Search in local database first
      const stocks = await DatabaseService.searchStocks(query);

      if (stocks.length > 0) {
        return stocks.map(stock => ({
          symbol: stock.symbol,
          name: stock.name
        }));
      }

      // If no results found locally, return empty array
      // In a real implementation, you might want to trigger a scrape for new stocks
      return [];
    } catch (error) {
      console.error(`Error searching for stocks: ${query}`, error);
      return [];
    }
  }

}


