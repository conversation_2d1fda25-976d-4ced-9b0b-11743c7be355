'use client';

import React, { useState } from 'react';

interface SyncResult {
  symbol: string;
  status: 'success' | 'failed';
  data?: any;
  error?: string;
}

interface BatchSyncResult {
  summary: {
    total: number;
    successful: number;
    failed: number;
    success_rate: string;
  };
  results: SyncResult[];
  errors: SyncResult[];
}

export default function PolygonManager() {
  const [symbol, setSymbol] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [batchLoading, setBatchLoading] = useState(false);
  const [batchResults, setBatchResults] = useState<BatchSyncResult | null>(null);

  const popularStocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX', 'AMD', 'CRM'];

  const testSnapshot = async () => {
    if (!symbol.trim()) {
      setError('Please enter a symbol');
      return;
    }

    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch(`/api/polygon/snapshot?symbol=${symbol.toUpperCase()}`);
      const data = await response.json();

      if (response.ok) {
        setResults({ type: 'snapshot', ...data });
      } else {
        setError(data.error || 'Failed to fetch snapshot');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const testDetails = async () => {
    if (!symbol.trim()) {
      setError('Please enter a symbol');
      return;
    }

    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch(`/api/polygon/details?symbol=${symbol.toUpperCase()}`);
      const data = await response.json();

      if (response.ok) {
        setResults({ type: 'details', ...data });
      } else {
        setError(data.error || 'Failed to fetch details');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const syncToDatabase = async () => {
    if (!symbol.trim()) {
      setError('Please enter a symbol');
      return;
    }

    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch('/api/polygon/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ symbol: symbol.toUpperCase() }),
      });

      const data = await response.json();

      if (response.ok) {
        setResults({ type: 'sync', ...data });
      } else {
        setError(data.error || 'Failed to sync data');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const batchSyncPopularStocks = async () => {
    setBatchLoading(true);
    setError(null);
    setBatchResults(null);

    try {
      const response = await fetch('/api/polygon/batch-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ symbols: popularStocks }),
      });

      const data = await response.json();

      if (response.ok) {
        setBatchResults(data);
      } else {
        setError(data.error || 'Failed to batch sync');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setBatchLoading(false);
    }
  };

  const testMarketStatus = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch('/api/polygon/market-status');
      const data = await response.json();

      if (response.ok) {
        setResults({ type: 'market-status', ...data });
      } else {
        setError(data.error || 'Failed to fetch market status');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Polygon.io Data Manager</h2>
        
        {/* Market Status */}
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Market Status</h3>
          <button
            onClick={testMarketStatus}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Loading...' : 'Check Market Status'}
          </button>
        </div>

        {/* Single Stock Testing */}
        <div className="space-y-4">
          <div>
            <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
              Stock Symbol
            </label>
            <div className="flex gap-2 flex-wrap">
              <input
                type="text"
                id="symbol"
                value={symbol}
                onChange={(e) => setSymbol(e.target.value.toUpperCase())}
                placeholder="Enter symbol (e.g., AAPL)"
                className="flex-1 min-w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              />
              <button
                onClick={testSnapshot}
                disabled={loading || !symbol.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Loading...' : 'Test Snapshot'}
              </button>
              <button
                onClick={testDetails}
                disabled={loading || !symbol.trim()}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Loading...' : 'Test Details'}
              </button>
              <button
                onClick={syncToDatabase}
                disabled={loading || !symbol.trim()}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Syncing...' : 'Sync to Database'}
              </button>
            </div>
          </div>

          {/* Batch Sync */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Batch Sync Popular Stocks</h3>
            <p className="text-sm text-gray-600 mb-4">
              Sync the following popular stocks: {popularStocks.join(', ')}
            </p>
            <button
              onClick={batchSyncPopularStocks}
              disabled={batchLoading}
              className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {batchLoading ? 'Syncing... (this may take several minutes)' : 'Load Popular Stocks'}
            </button>
            {batchLoading && (
              <p className="text-sm text-gray-600 mt-2">
                ⏱️ This will take approximately {popularStocks.length * 12} seconds due to rate limiting...
              </p>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Results Display */}
        {results && (
          <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Results ({results.type || 'unknown'})
            </h3>
            <pre className="text-sm text-gray-700 overflow-auto max-h-96">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        )}

        {/* Batch Results Display */}
        {batchResults && (
          <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Batch Sync Results</h3>
            
            {/* Summary */}
            <div className="grid grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{batchResults.summary.total}</div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{batchResults.summary.successful}</div>
                <div className="text-sm text-gray-600">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{batchResults.summary.failed}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{batchResults.summary.success_rate}</div>
                <div className="text-sm text-gray-600">Success Rate</div>
              </div>
            </div>

            {/* Detailed Results */}
            <div className="space-y-2">
              {batchResults.results.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded">
                  <span className="font-medium text-green-800">{result.symbol}</span>
                  <span className="text-sm text-green-600">✅ Success</span>
                </div>
              ))}
              {batchResults.errors.map((error, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-red-50 border border-red-200 rounded">
                  <span className="font-medium text-red-800">{error.symbol}</span>
                  <span className="text-sm text-red-600">❌ {error.error}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* API Information */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-green-900 mb-2">Polygon.io API Information</h3>
        <div className="text-sm text-green-800 space-y-1">
          <p><strong>Rate Limit:</strong> 5 requests per minute (free tier)</p>
          <p><strong>Delay Between Requests:</strong> 12 seconds</p>
          <p><strong>Data Source:</strong> Real-time and historical stock market data from all major US exchanges</p>
          <p><strong>Coverage:</strong> 19 major stock exchanges + dark pools + FINRA + OTC markets</p>
          <p><strong>Data Quality:</strong> Professional-grade, direct from exchanges</p>
        </div>
      </div>
    </div>
  );
}
