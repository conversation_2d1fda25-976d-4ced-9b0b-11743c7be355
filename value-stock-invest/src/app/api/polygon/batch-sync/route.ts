import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { symbols } = await request.json();

    if (!symbols || !Array.isArray(symbols) || symbols.length === 0) {
      return NextResponse.json(
        { error: 'Symbols array is required and must not be empty' },
        { status: 400 }
      );
    }

    if (symbols.length > 10) {
      return NextResponse.json(
        { error: 'Maximum 10 symbols allowed per batch' },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    console.log(`Starting batch sync for ${symbols.length} symbols...`);

    // Process symbols sequentially to respect rate limits
    for (const symbol of symbols) {
      try {
        console.log(`Syncing ${symbol}...`);
        
        // Call the single sync endpoint
        const response = await fetch(`${request.nextUrl.origin}/api/polygon/sync`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ symbol }),
        });

        const result = await response.json();

        if (response.ok) {
          results.push({
            symbol: symbol.toUpperCase(),
            status: 'success',
            data: result.data
          });
          console.log(`✅ Successfully synced ${symbol}`);
        } else {
          errors.push({
            symbol: symbol.toUpperCase(),
            status: 'failed',
            error: result.error || 'Unknown error'
          });
          console.log(`❌ Failed to sync ${symbol}: ${result.error}`);
        }

      } catch (error) {
        errors.push({
          symbol: symbol.toUpperCase(),
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        console.log(`❌ Error syncing ${symbol}:`, error);
      }

      // Add delay between requests (handled by polygonService rate limiting)
      // Additional small delay for batch processing
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const summary = {
      total: symbols.length,
      successful: results.length,
      failed: errors.length,
      success_rate: `${Math.round((results.length / symbols.length) * 100)}%`
    };

    console.log(`Batch sync completed: ${summary.successful}/${summary.total} successful`);

    return NextResponse.json({
      success: true,
      summary,
      results,
      errors,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Polygon batch sync API error:', error);
    return NextResponse.json(
      { error: 'Failed to process batch sync' },
      { status: 500 }
    );
  }
}
