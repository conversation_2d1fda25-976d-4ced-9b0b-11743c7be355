import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      );
    }

    const snapshot = await polygonService.getTickerSnapshot(symbol.toUpperCase());

    if (!snapshot) {
      return NextResponse.json(
        { error: `No data found for symbol: ${symbol}` },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: snapshot,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Polygon snapshot API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stock snapshot' },
      { status: 500 }
    );
  }
}
