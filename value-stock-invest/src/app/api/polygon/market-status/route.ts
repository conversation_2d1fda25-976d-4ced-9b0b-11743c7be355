import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';

export async function GET(request: NextRequest) {
  try {
    const marketStatus = await polygonService.getMarketStatus();

    if (!marketStatus) {
      return NextResponse.json(
        { error: 'Failed to fetch market status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: marketStatus,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Polygon market status API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch market status' },
      { status: 500 }
    );
  }
}
