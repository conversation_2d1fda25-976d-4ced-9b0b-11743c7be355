import { NextRequest, NextResponse } from 'next/server';
import { polygonService } from '@/lib/polygonService';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  let upperSymbol = 'UNKNOWN';
  
  try {
    const { symbol } = await request.json();

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol is required' },
        { status: 400 }
      );
    }

    upperSymbol = symbol.toUpperCase();
    console.log(`Starting simple sync for ${upperSymbol}...`);

    // Fetch company details from Polygon.io
    const details = await polygonService.getTickerDetails(upperSymbol);

    if (!details) {
      return NextResponse.json(
        { error: `No company details found for symbol: ${symbol}` },
        { status: 404 }
      );
    }

    console.log(`Got details for ${upperSymbol}: ${details.name}`);

    // Prepare stock data
    const stockData = {
      symbol: upperSymbol,
      name: details.name || upperSymbol,
      sector: details.sic_description || 'Unknown',
      industry: details.sic_description || 'Unknown',
      market_cap: details.market_cap ? Math.floor(Number(details.market_cap)) : null,
      description: details.description || null,
      website: details.homepage_url || null,
      employees: details.total_employees || null,
      exchange: details.primary_exchange || 'Unknown',
      currency: details.currency_name || 'USD',
      country: 'US',
      updated_at: new Date().toISOString()
    };

    // Prepare basic quote data (without real-time prices) - matching your schema
    const quoteData = {
      symbol: upperSymbol,
      price: 0, // Will be updated when real-time data is available
      change_amount: 0, // Your schema uses change_amount instead of change
      change_percent: 0,
      volume: 0,
      high: 0,
      low: 0,
      open: 0,
      previous_close: 0,
      market_cap: details.market_cap ? Math.floor(Number(details.market_cap)) : null,
      currency: details.currency_name || 'USD',
      updated_at: new Date().toISOString()
    };

    // Prepare financial data - matching your existing schema
    const financialData = {
      symbol: upperSymbol,
      shares_outstanding: details.share_class_shares_outstanding || null,
      updated_at: new Date().toISOString()
      // Note: market_cap is stored in stocks table, not stock_financials
    };

    console.log(`Upserting data for ${upperSymbol}...`);

    // Upsert to database - handle each table appropriately
    const { error: stockError } = await supabase
      .from('stocks')
      .upsert(stockData, { onConflict: 'symbol' });

    if (stockError) {
      console.error('Error upserting stock:', stockError);
      throw stockError;
    }

    // For stock_quotes, delete existing and insert new (since no unique constraint on symbol)
    const { error: deleteError } = await supabase
      .from('stock_quotes')
      .delete()
      .eq('symbol', upperSymbol);

    if (deleteError) {
      console.error('Error deleting old quote:', deleteError);
      // Continue anyway, might be first time
    }

    const { error: quoteError } = await supabase
      .from('stock_quotes')
      .insert(quoteData);

    if (quoteError) {
      console.error('Error inserting quote:', quoteError);
      throw quoteError;
    }

    // For stock_financials, delete existing and insert new (since no unique constraint on symbol)
    const { error: deleteFinError } = await supabase
      .from('stock_financials')
      .delete()
      .eq('symbol', upperSymbol);

    if (deleteFinError) {
      console.error('Error deleting old financials:', deleteFinError);
      // Continue anyway, might be first time
    }

    const { error: financialError } = await supabase
      .from('stock_financials')
      .insert(financialData);

    if (financialError) {
      console.error('Error inserting financials:', financialError);
      throw financialError;
    }

    // Log activity - using the new scrape_activity table
    await supabase
      .from('scrape_activity')
      .insert({
        symbol: upperSymbol,
        status: 'success',
        data_source: 'polygon',
        records_updated: 3,
        created_at: new Date().toISOString()
      });

    console.log(`✅ Successfully synced ${upperSymbol}`);

    return NextResponse.json({
      success: true,
      symbol: upperSymbol,
      data: {
        stock: stockData,
        quote: quoteData,
        financials: financialData
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Polygon simple sync API error:', error);
    
    // Log failed activity - using the new scrape_activity table
    try {
      await supabase
        .from('scrape_activity')
        .insert({
          symbol: upperSymbol,
          status: 'failed',
          data_source: 'polygon',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          created_at: new Date().toISOString()
        });
    } catch (logError) {
      console.error('Error logging failed activity:', logError);
    }

    return NextResponse.json(
      { 
        error: 'Failed to sync stock data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
